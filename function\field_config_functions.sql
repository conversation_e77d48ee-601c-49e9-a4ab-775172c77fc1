-- =====================================================
-- 字段配置函数集合
-- 功能：根据维度类型和产业类型动态配置字段
-- 创建时间：2025-01-06
-- 重构目标：消除重复的字段配置逻辑
-- =====================================================

-- 字段配置记录类型定义
CREATE TYPE fin_dm_opt_foi.field_config_type AS (
    -- 字段包含标志
    include_lv2 BOOLEAN,
    include_lv3 BOOLEAN,
    include_lv4 BOOLEAN,
    include_l1_l2 BOOLEAN,
    include_dimension BOOLEAN,
    include_dimension_sub BOOLEAN,
    include_dimension_detail BOOLEAN,
    include_spart BOOLEAN,
    include_coa BOOLEAN,
    
    -- 字段列表（用于SQL构建）
    select_fields TEXT,
    insert_fields TEXT,
    group_fields TEXT,
    
    -- 条件字段
    join_conditions TEXT
);

-- 获取字段配置函数
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.get_field_config(
    p_dimension_type VARCHAR,
    p_industry_flag VARCHAR
)
RETURNS fin_dm_opt_foi.field_config_type
LANGUAGE plpgsql
AS $$
DECLARE
    config fin_dm_opt_foi.field_config_type;
    base_fields TEXT;
    optional_fields TEXT;
BEGIN
    -- 记录函数调用日志
    RAISE NOTICE '获取字段配置 - 维度类型: %, 产业类型: %', p_dimension_type, p_industry_flag;
    
    -- 基础字段（所有配置都包含）
    base_fields := 'VERSION_ID, PERIOD_YEAR, PERIOD_ID, ' ||
                   'LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, ' ||
                   'LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME, ' ||
                   'L3_CEG_CODE, L3_CEG_CN_NAME, L3_CEG_SHORT_CN_NAME, ' ||
                   'L4_CEG_CODE, L4_CEG_CN_NAME, L4_CEG_SHORT_CN_NAME, ' ||
                   'CATEGORY_CODE, CATEGORY_CN_NAME, ITEM_CODE, ITEM_CN_NAME, ' ||
                   'SHIP_QUANTITY, COST_AMT, OVERSEA_FLAG, ' ||
                   'LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV0_PROD_LIST_EN_NAME';
    
    -- 初始化配置
    config.include_lv2 := TRUE;
    config.include_lv3 := TRUE;
    config.include_lv4 := FALSE; -- 默认不包含LV4，IAS特殊处理
    config.include_l1_l2 := TRUE;
    config.include_dimension := TRUE;
    config.include_dimension_sub := TRUE;
    config.include_dimension_detail := TRUE;
    config.include_spart := TRUE;
    config.include_coa := TRUE;
    
    -- 根据维度类型调整配置
    CASE p_dimension_type
        WHEN 'U' THEN -- 通用颗粒度
            config.include_l1_l2 := FALSE;
            config.include_dimension := FALSE;
            config.include_dimension_sub := FALSE;
            config.include_dimension_detail := FALSE;
            config.include_spart := FALSE;
            config.include_coa := FALSE;
            
        WHEN 'P' THEN -- 盈利颗粒度
            config.include_lv3 := FALSE;
            config.include_dimension := FALSE;
            config.include_dimension_sub := FALSE;
            config.include_dimension_detail := FALSE;
            config.include_spart := FALSE;
            config.include_coa := FALSE;
            
        WHEN 'D' THEN -- 量纲颗粒度
            config.include_l1_l2 := FALSE;
            -- 量纲颗粒度保留所有量纲相关字段
    END CASE;
    
    -- 根据产业类型调整配置
    CASE p_industry_flag
        WHEN 'I' THEN -- ICT
            config.include_lv4 := FALSE;
            IF p_dimension_type = 'D' THEN
                config.include_coa := FALSE; -- ICT量纲颗粒度不要COA
            END IF;
            
        WHEN 'E' THEN -- 数字能源
            config.include_lv4 := FALSE;
            -- 数字能源保留COA字段
            
        WHEN 'IAS' THEN -- IAS
            config.include_lv4 := TRUE; -- IAS包含LV4
            IF p_dimension_type = 'D' THEN
                config.include_coa := FALSE; -- IAS量纲颗粒度不要COA
            END IF;
    END CASE;
    
    -- 构建字段列表
    optional_fields := '';
    
    IF config.include_lv2 THEN
        optional_fields := optional_fields || ', LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME';
    END IF;
    
    IF config.include_lv3 THEN
        optional_fields := optional_fields || ', LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME';
    END IF;
    
    IF config.include_lv4 THEN
        optional_fields := optional_fields || ', LV4_PROD_RND_TEAM_CODE, LV4_PROD_RD_TEAM_CN_NAME';
    END IF;
    
    IF config.include_l1_l2 THEN
        optional_fields := optional_fields || ', L1_NAME, L2_NAME';
    END IF;
    
    IF config.include_dimension THEN
        optional_fields := optional_fields || ', DIMENSION_CODE, DIMENSION_CN_NAME, DIMENSION_EN_NAME';
    END IF;
    
    IF config.include_dimension_sub THEN
        optional_fields := optional_fields || ', DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, DIMENSION_SUBCATEGORY_EN_NAME';
    END IF;
    
    IF config.include_dimension_detail THEN
        optional_fields := optional_fields || ', DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, DIMENSION_SUB_DETAIL_EN_NAME';
    END IF;
    
    IF config.include_spart THEN
        optional_fields := optional_fields || ', SPART_CODE, SPART_CN_NAME';
    END IF;
    
    IF config.include_coa THEN
        optional_fields := optional_fields || ', COA_CODE, COA_CN_NAME';
    END IF;
    
    -- 组装完整字段列表
    config.select_fields := base_fields || optional_fields;
    config.insert_fields := base_fields || optional_fields;
    config.group_fields := base_fields || optional_fields;
    
    -- 构建JOIN条件
    config.join_conditions := '';
    IF config.include_lv2 THEN
        config.join_conditions := config.join_conditions || ' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE';
    END IF;
    
    IF config.include_lv3 THEN
        config.join_conditions := config.join_conditions || ' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE';
    END IF;
    
    IF config.include_lv4 THEN
        config.join_conditions := config.join_conditions || ' AND A.LV4_PROD_RND_TEAM_CODE = B.LV4_PROD_RND_TEAM_CODE';
    END IF;
    
    IF config.include_dimension THEN
        config.join_conditions := config.join_conditions || ' AND A.DIMENSION_CODE = B.DIMENSION_CODE';
    END IF;
    
    IF config.include_dimension_sub THEN
        config.join_conditions := config.join_conditions || ' AND A.DIMENSION_SUBCATEGORY_CODE = B.DIMENSION_SUBCATEGORY_CODE';
    END IF;
    
    IF config.include_dimension_detail THEN
        config.join_conditions := config.join_conditions || ' AND A.DIMENSION_SUB_DETAIL_CODE = B.DIMENSION_SUB_DETAIL_CODE';
    END IF;
    
    IF config.include_spart THEN
        config.join_conditions := config.join_conditions || ' AND A.SPART_CODE = B.SPART_CODE';
    END IF;
    
    IF config.include_coa THEN
        config.join_conditions := config.join_conditions || ' AND A.COA_CODE = B.COA_CODE';
    END IF;
    
    RAISE NOTICE '字段配置获取成功 - 包含字段数: %', 
                 array_length(string_to_array(config.select_fields, ','), 1);
    
    RETURN config;
END;
$$;

-- 构建基础数据SQL
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.build_base_data_sql(
    p_from_table VARCHAR,
    p_join_table VARCHAR,
    p_period_id BIGINT,
    p_caliber_flag VARCHAR,
    p_dimension_type VARCHAR,
    p_keystr VARCHAR,
    p_field_config fin_dm_opt_foi.field_config_type
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    sql_text TEXT;
    cost_calculation TEXT;
    dimension_filter TEXT;
BEGIN
    -- 构建成本计算逻辑
    IF p_caliber_flag = 'R' THEN
        cost_calculation := 'TO_NUMBER(GS_DECRYPT(T2.RMB_AVG_AMT,''' || p_keystr || ''', ''AES128'', ''CBC'', ''SHA256''))*T.SHIP_QUANTITY';
    ELSE
        cost_calculation := 'TO_NUMBER(GS_DECRYPT(T2.RMB_COST_AMT,''' || p_keystr || ''', ''AES128'', ''CBC'', ''SHA256''))';
    END IF;
    
    -- 构建量纲过滤条件
    IF p_dimension_type = 'D' THEN
        dimension_filter := ' AND DIMENSION_CODE IS NOT NULL';
    ELSE
        dimension_filter := '';
    END IF;
    
    -- 构建完整SQL
    sql_text := 'INSERT INTO BASE_DATA_TEMP (' || p_field_config.insert_fields || ') ' ||
                'SELECT ' || 
                REPLACE(p_field_config.select_fields, 'COST_AMT', cost_calculation || ' AS COST_AMT') ||
                ' FROM ' || p_from_table || ' T ' ||
                ' LEFT JOIN ' || p_join_table || ' T2 ON T.PRIMARY_ID = T2.PRIMARY_ID ' ||
                ' WHERE T.PERIOD_ID = ' || p_period_id ||
                dimension_filter;
    
    RETURN sql_text;
END;
$$;

-- 构建全球数据SQL
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.build_global_data_sql(
    p_field_config fin_dm_opt_foi.field_config_type
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    sql_text TEXT;
BEGIN
    sql_text := 'INSERT INTO BASE_DATA_TEMP (' || p_field_config.insert_fields || ') ' ||
                'SELECT ' ||
                REPLACE(p_field_config.select_fields, 'OVERSEA_FLAG', '''G'' AS OVERSEA_FLAG') ||
                ' FROM BASE_DATA_TEMP';

    RETURN sql_text;
END;
$$;

-- 构建最终插入SQL
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.build_final_insert_sql(
    p_to_table VARCHAR,
    p_field_config fin_dm_opt_foi.field_config_type
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    sql_text TEXT;
    final_fields TEXT;
BEGIN
    -- 添加VIEW_FLAG字段到字段列表
    final_fields := p_field_config.insert_fields || ', VIEW_FLAG';

    sql_text := 'INSERT INTO ' || p_to_table || ' (' || final_fields || ') ' ||
                'SELECT ' || p_field_config.select_fields || ', VIEW_FLAG ' ||
                'FROM VIEW_DATA_TEMP';

    RETURN sql_text;
END;
$$;
