-- =====================================================
-- 业务逻辑配置文件
-- 功能：定义业务逻辑配置函数，简化主函数的复杂度
-- 创建时间：2025-01-06
-- 修改记录：初始创建
-- =====================================================

-- 获取源表和加密表配置
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.get_source_table_config(
    p_industry_flag VARCHAR,
    p_caliber_flag VARCHAR,
    OUT source_table VARCHAR,
    OUT encrypt_table VARCHAR
) 
RETURNS RECORD
LANGUAGE plpgsql
AS $$
BEGIN
    -- 记录函数调用日志
    RAISE NOTICE '获取源表配置 - 产业类型: %, 业务口径: %', p_industry_flag, p_caliber_flag;
    
    -- ICT产业配置
    IF p_industry_flag = 'I' THEN
        IF p_caliber_flag = 'C' THEN
            source_table := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
            encrypt_table := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
        ELSIF p_caliber_flag = 'R' THEN
            source_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T';
            encrypt_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
        END IF;
    -- 数字能源产业配置
    ELSIF p_industry_flag = 'E' THEN
        IF p_caliber_flag = 'C' THEN
            source_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T';
            encrypt_table := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
        ELSIF p_caliber_flag = 'R' THEN
            source_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_REVENUE_ITEM_SHIP_DTL_T';
            encrypt_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
        END IF;
    -- IAS产业配置
    ELSIF p_industry_flag = 'IAS' THEN
        IF p_caliber_flag = 'C' THEN
            source_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T';
            encrypt_table := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
        ELSIF p_caliber_flag = 'R' THEN
            source_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_REVENUE_ITEM_SHIP_DTL_T';
            encrypt_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
        END IF;
    END IF;
    
    -- 验证配置结果
    IF source_table IS NULL OR encrypt_table IS NULL THEN
        RAISE EXCEPTION '无效的产业类型或业务口径组合: 产业类型=%, 业务口径=%', p_industry_flag, p_caliber_flag;
    END IF;
    
    RAISE NOTICE '源表配置获取成功 - 源表: %, 加密表: %', source_table, encrypt_table;
END;
$$;

-- 获取目标表和视角配置
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.get_target_table_config(
    p_industry_flag VARCHAR,
    p_caliber_flag VARCHAR,
    p_dimension_type VARCHAR,
    OUT target_table VARCHAR,
    OUT view_begin INTEGER,
    OUT view_end INTEGER
) 
RETURNS RECORD
LANGUAGE plpgsql
AS $$
BEGIN
    -- 记录函数调用日志
    RAISE NOTICE '获取目标表配置 - 产业类型: %, 业务口径: %, 维度类型: %', 
                 p_industry_flag, p_caliber_flag, p_dimension_type;
    
    -- ICT产业配置
    IF p_industry_flag = 'I' THEN
        IF p_dimension_type = 'U' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CU';
            view_begin := 0; view_end := 3;
        ELSIF p_dimension_type = 'P' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CP';
            view_begin := 3; view_end := 4;
        ELSIF p_dimension_type = 'D' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS';
            view_begin := 0; view_end := 11;
        ELSIF p_dimension_type = 'U' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RU';
            view_begin := 0; view_end := 3;
        ELSIF p_dimension_type = 'P' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RP';
            view_begin := 3; view_end := 4;
        ELSIF p_dimension_type = 'D' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS';
            view_begin := 0; view_end := 11;
        END IF;
    -- 数字能源产业配置
    ELSIF p_industry_flag = 'E' THEN
        IF p_dimension_type = 'U' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CU';
            view_begin := 0; view_end := 3;
        ELSIF p_dimension_type = 'P' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CP';
            view_begin := 3; view_end := 4;
        ELSIF p_dimension_type = 'D' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CD_DMS';
            view_begin := 0; view_end := 12;
        ELSIF p_dimension_type = 'U' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RU';
            view_begin := 0; view_end := 3;
        ELSIF p_dimension_type = 'P' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RP';
            view_begin := 3; view_end := 4;
        ELSIF p_dimension_type = 'D' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RD_DMS';
            view_begin := 0; view_end := 12;
        END IF;
    -- IAS产业配置
    ELSIF p_industry_flag = 'IAS' THEN
        IF p_dimension_type = 'U' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CU';
            view_begin := 0; view_end := 4;
        ELSIF p_dimension_type = 'P' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CP';
            view_begin := 3; view_end := 4;
        ELSIF p_dimension_type = 'D' AND p_caliber_flag = 'C' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CD_DMS';
            view_begin := 0; view_end := 12;
        ELSIF p_dimension_type = 'U' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RU';
            view_begin := 0; view_end := 4;
        ELSIF p_dimension_type = 'P' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RP';
            view_begin := 3; view_end := 4;
        ELSIF p_dimension_type = 'D' AND p_caliber_flag = 'R' THEN
            target_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RD_DMS';
            view_begin := 0; view_end := 12;
        END IF;
    END IF;
    
    -- 验证配置结果
    IF target_table IS NULL OR view_begin IS NULL OR view_end IS NULL THEN
        RAISE EXCEPTION '无效的参数组合: 产业类型=%, 业务口径=%, 维度类型=%', 
                        p_industry_flag, p_caliber_flag, p_dimension_type;
    END IF;
    
    RAISE NOTICE '目标表配置获取成功 - 目标表: %, 视角范围: %-%', target_table, view_begin, view_end;
END;
$$;

-- 获取版本表配置
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.get_version_table_name(
    p_industry_flag VARCHAR
) 
RETURNS VARCHAR
LANGUAGE plpgsql
AS $$
DECLARE
    version_table VARCHAR(100);
BEGIN
    -- 记录函数调用日志
    RAISE NOTICE '获取版本表配置 - 产业类型: %', p_industry_flag;
    
    CASE p_industry_flag
        WHEN 'I' THEN version_table := 'DM_FOC_VERSION_INFO_T';
        WHEN 'E' THEN version_table := 'DM_FOC_ENERGY_VERSION_INFO_T';
        WHEN 'IAS' THEN version_table := 'DM_FOC_IAS_VERSION_INFO_T';
        ELSE 
            RAISE EXCEPTION '无效的产业类型: %', p_industry_flag;
    END CASE;
    
    RAISE NOTICE '版本表配置获取成功 - 版本表: %', version_table;
    RETURN version_table;
END;
$$;
