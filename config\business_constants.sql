-- =====================================================
-- 业务常量配置文件
-- 功能：抽离硬编码的业务常量，便于维护和管理
-- 创建时间：2025-01-06
-- 修改记录：初始创建
-- =====================================================

-- 产业类型常量 (F_INDUSTRY_FLAG)
-- ICT产业
DECLARE CONST_INDUSTRY_ICT CONSTANT VARCHAR(10) := 'I';
-- 数字能源产业  
DECLARE CONST_INDUSTRY_ENERGY CONSTANT VARCHAR(10) := 'E';
-- IAS产业
DECLARE CONST_INDUSTRY_IAS CONSTANT VARCHAR(10) := 'IAS';

-- 维度类型常量 (F_DIMENSION_TYPE)
-- 通用颗粒度
DECLARE CONST_DIMENSION_UNIVERSAL CONSTANT VARCHAR(10) := 'U';
-- 盈利颗粒度
DECLARE CONST_DIMENSION_PROFIT CONSTANT VARCHAR(10) := 'P';
-- 量纲颗粒度
DECLARE CONST_DIMENSION_DMS CONSTANT VARCHAR(10) := 'D';

-- 业务口径常量 (F_CALIBER_FLAG)
-- 发货成本
DECLARE CONST_CALIBER_COST CONSTANT VARCHAR(10) := 'C';
-- 收入时点
DECLARE CONST_CALIBER_REVENUE CONSTANT VARCHAR(10) := 'R';

-- 视角配置常量
-- 通用颗粒度视角范围
DECLARE CONST_VIEW_UNIVERSAL_BEGIN CONSTANT INTEGER := 0;
DECLARE CONST_VIEW_UNIVERSAL_END_ICT CONSTANT INTEGER := 3;
DECLARE CONST_VIEW_UNIVERSAL_END_ENERGY CONSTANT INTEGER := 3;
DECLARE CONST_VIEW_UNIVERSAL_END_IAS CONSTANT INTEGER := 4;

-- 盈利颗粒度视角范围
DECLARE CONST_VIEW_PROFIT_BEGIN CONSTANT INTEGER := 3;
DECLARE CONST_VIEW_PROFIT_END CONSTANT INTEGER := 4;

-- 量纲颗粒度视角范围
DECLARE CONST_VIEW_DMS_BEGIN CONSTANT INTEGER := 0;
DECLARE CONST_VIEW_DMS_END_ICT CONSTANT INTEGER := 11;
DECLARE CONST_VIEW_DMS_END_ENERGY CONSTANT INTEGER := 12;
DECLARE CONST_VIEW_DMS_END_IAS CONSTANT INTEGER := 12;

-- 表名配置常量
-- ICT产业表名
DECLARE CONST_TABLE_ICT_BOM_SOURCE CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
DECLARE CONST_TABLE_ICT_REVENUE_SOURCE CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T';
DECLARE CONST_TABLE_ICT_BOM_ENCRYPT CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
DECLARE CONST_TABLE_ICT_REVENUE_ENCRYPT CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';

-- 数字能源产业表名
DECLARE CONST_TABLE_ENERGY_BOM_SOURCE CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T';
DECLARE CONST_TABLE_ENERGY_REVENUE_SOURCE CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_REVENUE_ITEM_SHIP_DTL_T';
DECLARE CONST_TABLE_ENERGY_BOM_ENCRYPT CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
DECLARE CONST_TABLE_ENERGY_REVENUE_ENCRYPT CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';

-- IAS产业表名
DECLARE CONST_TABLE_IAS_BOM_SOURCE CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T';
DECLARE CONST_TABLE_IAS_REVENUE_SOURCE CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_REVENUE_ITEM_SHIP_DTL_T';
DECLARE CONST_TABLE_IAS_BOM_ENCRYPT CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
DECLARE CONST_TABLE_IAS_REVENUE_ENCRYPT CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';

-- 目标表名配置
-- ICT产业目标表
DECLARE CONST_TABLE_ICT_TARGET_CU CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CU';
DECLARE CONST_TABLE_ICT_TARGET_CP CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CP';
DECLARE CONST_TABLE_ICT_TARGET_CD_DMS CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS';
DECLARE CONST_TABLE_ICT_TARGET_RU CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RU';
DECLARE CONST_TABLE_ICT_TARGET_RP CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RP';
DECLARE CONST_TABLE_ICT_TARGET_RD_DMS CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS';

-- 数字能源产业目标表
DECLARE CONST_TABLE_ENERGY_TARGET_CU CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CU';
DECLARE CONST_TABLE_ENERGY_TARGET_CP CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CP';
DECLARE CONST_TABLE_ENERGY_TARGET_CD_DMS CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CD_DMS';
DECLARE CONST_TABLE_ENERGY_TARGET_RU CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RU';
DECLARE CONST_TABLE_ENERGY_TARGET_RP CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RP';
DECLARE CONST_TABLE_ENERGY_TARGET_RD_DMS CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RD_DMS';

-- IAS产业目标表
DECLARE CONST_TABLE_IAS_TARGET_CU CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CU';
DECLARE CONST_TABLE_IAS_TARGET_CP CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CP';
DECLARE CONST_TABLE_IAS_TARGET_CD_DMS CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CD_DMS';
DECLARE CONST_TABLE_IAS_TARGET_RU CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RU';
DECLARE CONST_TABLE_IAS_TARGET_RP CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RP';
DECLARE CONST_TABLE_IAS_TARGET_RD_DMS CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RD_DMS';

-- 版本表配置
DECLARE CONST_TABLE_ICT_VERSION CONSTANT VARCHAR(100) := 'DM_FOC_VERSION_INFO_T';
DECLARE CONST_TABLE_ENERGY_VERSION CONSTANT VARCHAR(100) := 'DM_FOC_ENERGY_VERSION_INFO_T';
DECLARE CONST_TABLE_IAS_VERSION CONSTANT VARCHAR(100) := 'DM_FOC_IAS_VERSION_INFO_T';

-- 业务规则常量
-- 排除的LV1重量级团队代码
DECLARE CONST_EXCLUDED_LV1_TEAMS CONSTANT VARCHAR(200) := '''101764'',''100005'',''135741'',''104237'',''133341''';

-- 集团标识常量
DECLARE CONST_GROUP_CODE CONSTANT VARCHAR(10) := 'GR';
DECLARE CONST_GROUP_CN_NAME CONSTANT VARCHAR(20) := '集团';
DECLARE CONST_GROUP_EN_NAME CONSTANT VARCHAR(20) := 'GROUP';

-- 系统配置常量
DECLARE CONST_YEARS_BACK CONSTANT INTEGER := 3; -- 系统年-3的年份
DECLARE CONST_SUCCESS_STATUS CONSTANT VARCHAR(10) := '1';
DECLARE CONST_FAIL_STATUS CONSTANT VARCHAR(10) := '0';
DECLARE CONST_SUCCESS_MESSAGE CONSTANT VARCHAR(20) := 'SUCCESS';

-- 加密相关常量
DECLARE CONST_ENCRYPT_ALGORITHM CONSTANT VARCHAR(20) := 'AES128';
DECLARE CONST_ENCRYPT_MODE CONSTANT VARCHAR(20) := 'CBC';
DECLARE CONST_ENCRYPT_HASH CONSTANT VARCHAR(20) := 'SHA256';
