-- =====================================================
-- 数据处理函数（重构版本）
-- 功能：处理ITEM月卷积发货额数据的核心逻辑
-- 创建时间：2025-01-06
-- 重构目标：简化数据处理逻辑，增强可读性和可维护性
-- =====================================================

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.process_item_data_refactored(
    p_industry_flag VARCHAR,
    p_period_id BIGINT,
    p_caliber_flag VARCHAR,
    p_dimension_type VARCHAR,
    p_keystr VARCHAR,
    p_from_table VARCHAR,
    p_join_table VARCHAR,
    p_to_table VARCHAR,
    p_version_id BIGINT,
    p_view_begin INTEGER,
    p_view_end INTEGER
)
RETURNS VOID
LANGUAGE plpgsql
AS $$

DECLARE
    -- 常量定义
    C_SP_NAME CONSTANT VARCHAR(500) := 'FIN_DM_OPT_FOI.PROCESS_ITEM_DATA_REFACTORED';
    C_SUCCESS_MSG CONSTANT VARCHAR(20) := 'SUCCESS';
    C_GROUP_CODE CONSTANT VARCHAR(10) := 'GR';
    C_GROUP_CN_NAME CONSTANT VARCHAR(20) := '集团';
    C_GROUP_EN_NAME CONSTANT VARCHAR(20) := 'GROUP';
    C_EXCLUDED_TEAMS CONSTANT VARCHAR(200) := '''101764'',''100005'',''135741'',''104237'',''133341''';
    
    -- 业务变量
    v_step_num BIGINT := 0;
    v_sql TEXT;
    v_view_num INTEGER;
    
    -- 字段配置变量（根据维度类型和产业类型动态设置）
    v_field_config RECORD;
    
BEGIN
    -- 记录数据处理开始日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '开始数据处理 - 源表：' || p_from_table || ', 目标表：' || p_to_table ||
                         ', 视角范围：' || p_view_begin || '-' || p_view_end
    );
    
    -- 创建基础数据临时表
    DROP TABLE IF EXISTS BASE_DATA_TEMP;
    CREATE TEMPORARY TABLE BASE_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV1_PROD_RND_TEAM_CODE VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV2_PROD_RND_TEAM_CODE VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        L1_NAME VARCHAR(100),
        L2_NAME VARCHAR(100),
        DIMENSION_CODE VARCHAR(100),
        DIMENSION_CN_NAME VARCHAR(100),
        DIMENSION_EN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(100),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(100),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
        DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200),
        SPART_CODE VARCHAR(50),
        SPART_CN_NAME VARCHAR(50),
        COA_CODE VARCHAR(50),
        COA_CN_NAME VARCHAR(600),
        L3_CEG_CODE VARCHAR(50),
        L3_CEG_CN_NAME VARCHAR(200),
        L3_CEG_SHORT_CN_NAME VARCHAR(200),
        L4_CEG_CODE VARCHAR(50),
        L4_CEG_CN_NAME VARCHAR(200),
        L4_CEG_SHORT_CN_NAME VARCHAR(200),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(500),
        SHIP_QUANTITY NUMERIC,
        COST_AMT NUMERIC,
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID, ITEM_CODE);
    
    -- 记录临时表创建成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '基础数据临时表创建成功'
    );
    
    -- 获取字段配置
    SELECT * INTO v_field_config 
    FROM fin_dm_opt_foi.get_field_config(p_dimension_type, p_industry_flag);
    
    -- 插入基础数据（包含国内和海外数据）
    v_sql := fin_dm_opt_foi.build_base_data_sql(
        p_from_table, p_join_table, p_period_id, p_caliber_flag, 
        p_dimension_type, p_keystr, v_field_config
    );
    
    EXECUTE v_sql;
    
    -- 记录基础数据插入成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '基础数据插入成功（国内+海外）',
        F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
        F_RESULT_STATUS => '1',
        F_ERRBUF => C_SUCCESS_MSG
    );
    
    -- 插入全球数据
    v_sql := fin_dm_opt_foi.build_global_data_sql(v_field_config);
    EXECUTE v_sql;
    
    -- 记录全球数据插入成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '全球数据插入成功',
        F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
        F_RESULT_STATUS => '1',
        F_ERRBUF => C_SUCCESS_MSG
    );
    
    -- 创建视角数据临时表
    DROP TABLE IF EXISTS VIEW_DATA_TEMP;
    CREATE TEMPORARY TABLE VIEW_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        DIMENSION_CODE VARCHAR(100),
        DIMENSION_CN_NAME VARCHAR(100),
        DIMENSION_EN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(100),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(100),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
        DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200),
        SPART_CODE VARCHAR(50),
        SPART_CN_NAME VARCHAR(50),
        COA_CODE VARCHAR(50),
        COA_CN_NAME VARCHAR(600),
        L3_CEG_CODE VARCHAR(50),
        L3_CEG_CN_NAME VARCHAR(200),
        L3_CEG_SHORT_CN_NAME VARCHAR(200),
        L4_CEG_CODE VARCHAR(50),
        L4_CEG_CN_NAME VARCHAR(200),
        L4_CEG_SHORT_CN_NAME VARCHAR(200),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(500),
        SHIP_QUANTITY NUMERIC,
        COST_AMT NUMERIC,
        VIEW_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS 
    DISTRIBUTE BY ROUNDROBIN;
    
    -- 记录视角数据临时表创建成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '视角数据临时表创建成功'
    );
    
    -- 循环处理各个视角
    FOR v_view_num IN p_view_begin..p_view_end LOOP
        -- 调用视角处理函数
        PERFORM fin_dm_opt_foi.process_view_data(
            v_view_num, p_dimension_type, p_industry_flag, 
            p_version_id, v_field_config
        );
        
        -- 记录视角处理完成日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '视角 ' || v_view_num || ' 处理完成',
            F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
            F_RESULT_STATUS => '1',
            F_ERRBUF => C_SUCCESS_MSG
        );
    END LOOP;
    
    -- 将视角数据插入目标表
    v_sql := fin_dm_opt_foi.build_final_insert_sql(p_to_table, v_field_config);
    EXECUTE v_sql;
    
    -- 记录最终数据插入成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '最终数据插入目标表成功 - 目标表：' || p_to_table,
        F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
        F_RESULT_STATUS => '1',
        F_ERRBUF => C_SUCCESS_MSG
    );
    
    -- 记录数据处理完成日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '数据处理完成 - 所有视角数据已成功处理并插入目标表'
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- 记录异常日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_CAL_LOG_DESC => '数据处理失败 - 错误信息：' || SQLERRM,
            F_RESULT_STATUS => '0',
            F_ERRBUF => SQLSTATE || ':' || SQLERRM
        );
        RAISE;
END;
$$;
