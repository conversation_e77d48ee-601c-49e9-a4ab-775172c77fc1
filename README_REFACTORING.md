# 月卷积发货额统计函数重构说明

## 重构概述

本次重构针对原始的 `f_dm_foc_mid_month_item_dms.sql` 函数进行了全面的代码优化和重构，主要目标是：

1. **抽离硬编码变量**：将 `F_INDUSTRY_FLAG = 'I'`、`F_DIMENSION_TYPE = 'D'`、`F_CALIBER_FLAG = 'C'` 等硬编码常量抽离到独立配置文件
2. **内联固定变量**：将不变的常量直接内联到代码中
3. **删除多余逻辑**：消除重复的条件判断和变量赋值
4. **增加详细日志**：在关键步骤添加详细日志，方便错误追溯

## 重构后的文件结构

### 1. 配置文件
- `config/business_constants.sql` - 业务常量配置
- `config/business_logic_config.sql` - 业务逻辑配置函数

### 2. 主函数
- `function/f_dm_foc_mid_month_item_dms_refactored.sql` - 重构后的主函数

### 3. 辅助函数
- `function/process_item_data_refactored.sql` - 数据处理核心逻辑
- `function/field_config_functions.sql` - 字段配置和SQL构建函数
- `function/view_processing_functions.sql` - 视角处理函数

## 主要改进点

### 1. 常量抽离
**原代码问题**：
```sql
IF F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'I' THEN
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
```

**重构后**：
```sql
-- 在 business_constants.sql 中定义
DECLARE CONST_INDUSTRY_ICT CONSTANT VARCHAR(10) := 'I';
DECLARE CONST_CALIBER_COST CONSTANT VARCHAR(10) := 'C';

-- 在业务逻辑中使用配置函数
SELECT * INTO source_config 
FROM fin_dm_opt_foi.get_source_table_config(f_industry_flag, f_caliber_flag);
```

### 2. 逻辑简化
**原代码问题**：
- 大量重复的 IF-ELSIF 嵌套判断
- 相同变量在不同条件下重复赋值
- 超过3000行的单一函数

**重构后**：
- 将复杂逻辑拆分为多个专门的配置函数
- 使用记录类型（RECORD）统一管理配置
- 主函数只负责流程控制，具体逻辑委托给专门函数

### 3. 日志增强
**原代码问题**：
```sql
PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
(F_SP_NAME => V_SP_NAME,
 F_STEP_NUM => V_STEP_MUM,
 F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据');
```

**重构后**：
```sql
PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
    F_SP_NAME => C_SP_NAME,
    F_STEP_NUM => v_step_num,
    F_CAL_LOG_DESC => '清空目标表数据成功 - 表名：' || v_to_table || 
                     ', 期间ID：' || f_period_id,
    F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
    F_RESULT_STATUS => x_result_status,
    F_ERRBUF => C_SUCCESS_MSG
);
```

### 4. 错误处理改进
**原代码问题**：
- 错误信息不够详细
- 缺少参数验证
- 异常处理不够精细

**重构后**：
- 增加详细的参数验证和错误提示
- 每个关键步骤都有异常处理
- 错误信息包含具体的参数值和上下文

## 核心函数说明

### 1. 配置函数
- `get_source_table_config()` - 根据产业类型和业务口径获取源表配置
- `get_target_table_config()` - 根据参数组合获取目标表和视角配置
- `get_version_table_name()` - 获取版本表名称

### 2. 字段配置函数
- `get_field_config()` - 根据维度类型和产业类型动态配置字段
- `build_base_data_sql()` - 构建基础数据查询SQL
- `build_global_data_sql()` - 构建全球数据查询SQL
- `build_final_insert_sql()` - 构建最终插入SQL

### 3. 视角处理函数
- `get_view_config()` - 获取视角配置
- `process_view_data()` - 处理单个视角的数据

## 使用方式

### 1. 部署顺序
```sql
-- 1. 部署配置文件
\i config/business_constants.sql
\i config/business_logic_config.sql

-- 2. 部署辅助函数
\i function/field_config_functions.sql
\i function/view_processing_functions.sql
\i function/process_item_data_refactored.sql

-- 3. 部署主函数
\i function/f_dm_foc_mid_month_item_dms_refactored.sql
```

### 2. 调用方式
```sql
-- 调用重构后的函数
SELECT fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    'I',        -- 产业类型：ICT
    202312,     -- 期间ID
    'C',        -- 业务口径：发货成本
    'D',        -- 维度类型：量纲颗粒度
    'your_key'  -- 加密密钥
);
```

## 性能优化

1. **减少SQL拼接**：通过预定义的字段配置减少动态SQL构建的复杂度
2. **优化临时表**：使用更合理的分布策略
3. **减少重复计算**：将配置计算结果缓存在记录类型中

## 维护优势

1. **配置集中管理**：所有业务常量在配置文件中统一管理
2. **逻辑模块化**：不同功能拆分为独立函数，便于单独测试和维护
3. **日志完善**：详细的日志记录便于问题定位和性能分析
4. **扩展性强**：新增产业类型或维度类型只需修改配置函数

## 测试建议

1. **单元测试**：对每个配置函数进行单独测试
2. **集成测试**：测试不同参数组合的完整流程
3. **性能测试**：对比重构前后的执行时间和资源消耗
4. **数据一致性测试**：确保重构后结果与原函数一致

## 注意事项

1. 重构后的函数名为 `f_dm_foc_mid_month_item_dms_refactored`，避免与原函数冲突
2. 需要确保所有依赖的类型和函数都已正确创建
3. 建议在测试环境充分验证后再部署到生产环境
4. 保留原函数作为备份，确保可以快速回滚
