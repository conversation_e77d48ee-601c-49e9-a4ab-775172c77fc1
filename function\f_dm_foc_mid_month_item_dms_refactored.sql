-- =====================================================
-- 重构后的月卷积发货额统计函数
-- 功能：分视角统计ITEM的月卷积发货额（重构版本）
-- 创建时间：2025-01-06
-- 重构目标：
--   1. 抽离硬编码变量到独立配置文件
--   2. 内联固定变量，删除多余逻辑
--   3. 增加详细日志方便追溯错误
--   4. 简化条件判断逻辑
-- =====================================================

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    f_industry_flag character varying, 
    f_period_id bigint, 
    f_caliber_flag character varying, 
    f_dimension_type character varying, 
    f_keystr character varying, 
    OUT x_result_status character varying
)
RETURNS character varying
LANGUAGE plpgsql
NOT FENCED NOT SHIPPABLE
AS $$

/*
功能描述：分视角统计ITEM的月卷积发货额（重构版本）
最后修改人：系统重构
背景描述：
  - 原函数存在大量硬编码变量和重复逻辑
  - 重构后抽离常量配置，简化逻辑，增强可维护性
修改记录：
  - 2025-01-06：重构原函数，抽离常量配置，优化逻辑结构
参数描述：
  - f_industry_flag：产业类型（I：ICT，E：数字能源，IAS：IAS）
  - f_caliber_flag：业务口径（R：收入时点，C：发货成本）
  - f_dimension_type：维度类型（U：通用颗粒度，P：盈利颗粒度，D：量纲颗粒度）
  - f_period_id：期间ID
  - f_keystr：加密密钥
  - x_result_status：执行结果状态
来源表：根据产业类型和业务口径动态确定
目标表：根据产业类型、业务口径和维度类型动态确定
*/

DECLARE
    -- 常量定义（内联固定值）
    C_SP_NAME CONSTANT VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_DMS_REFACTORED';
    C_SUCCESS_STATUS CONSTANT VARCHAR(10) := '1';
    C_FAIL_STATUS CONSTANT VARCHAR(10) := '0';
    C_SUCCESS_MSG CONSTANT VARCHAR(20) := 'SUCCESS';
    C_YEARS_BACK CONSTANT INTEGER := 3;
    C_GROUP_CODE CONSTANT VARCHAR(10) := 'GR';
    C_GROUP_CN_NAME CONSTANT VARCHAR(20) := '集团';
    C_GROUP_EN_NAME CONSTANT VARCHAR(20) := 'GROUP';
    C_EXCLUDED_TEAMS CONSTANT VARCHAR(200) := '''101764'',''100005'',''135741'',''104237'',''133341''';
    
    -- 业务变量
    v_step_num BIGINT := 0;
    v_begin_year BIGINT := EXTRACT(YEAR FROM CURRENT_TIMESTAMP) - C_YEARS_BACK;
    v_end_year BIGINT := EXTRACT(YEAR FROM CURRENT_TIMESTAMP);
    v_version_id BIGINT;
    v_sql TEXT;
    
    -- 表配置变量
    v_from_table VARCHAR(200);
    v_join_table VARCHAR(200);
    v_to_table VARCHAR(200);
    v_version_table VARCHAR(100);
    v_view_begin INTEGER;
    v_view_end INTEGER;
    
    -- 配置记录类型
    source_config RECORD;
    target_config RECORD;
    
BEGIN
    -- 初始化返回状态
    x_result_status := C_SUCCESS_STATUS;
    
    -- 记录函数开始执行日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => C_SP_NAME || '开始执行 - 产业类型：' || f_industry_flag || 
                         ', 维度类型：' || f_dimension_type || ', 业务口径：' || f_caliber_flag ||
                         ', 期间ID：' || f_period_id
    );
    
    -- 参数合法性验证
    IF f_dimension_type NOT IN ('U', 'P', 'D') THEN
        RAISE EXCEPTION '维度类型参数无效: %, 有效值: U, P, D', f_dimension_type;
    END IF;
    
    IF f_caliber_flag NOT IN ('C', 'R') THEN
        RAISE EXCEPTION '业务口径参数无效: %, 有效值: C, R', f_caliber_flag;
    END IF;
    
    IF f_industry_flag NOT IN ('E', 'I', 'IAS') THEN
        RAISE EXCEPTION '产业类型参数无效: %, 有效值: E, I, IAS', f_industry_flag;
    END IF;
    
    -- 记录参数验证通过日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '参数验证通过 - 所有输入参数格式正确'
    );
    
    -- 获取源表和加密表配置
    BEGIN
        SELECT * INTO source_config 
        FROM fin_dm_opt_foi.get_source_table_config(f_industry_flag, f_caliber_flag);
        
        v_from_table := source_config.source_table;
        v_join_table := source_config.encrypt_table;
        
        -- 记录源表配置获取成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '源表配置获取成功 - 源表：' || v_from_table || ', 加密表：' || v_join_table
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '获取源表配置失败: %', SQLERRM;
    END;
    
    -- 获取目标表和视角配置
    BEGIN
        SELECT * INTO target_config 
        FROM fin_dm_opt_foi.get_target_table_config(f_industry_flag, f_caliber_flag, f_dimension_type);
        
        v_to_table := target_config.target_table;
        v_view_begin := target_config.view_begin;
        v_view_end := target_config.view_end;
        
        -- 记录目标表配置获取成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '目标表配置获取成功 - 目标表：' || v_to_table || 
                             ', 视角范围：' || v_view_begin || '-' || v_view_end
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '获取目标表配置失败: %', SQLERRM;
    END;
    
    -- 获取版本表配置
    BEGIN
        v_version_table := fin_dm_opt_foi.get_version_table_name(f_industry_flag);
        
        -- 记录版本表配置获取成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '版本表配置获取成功 - 版本表：' || v_version_table
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '获取版本表配置失败: %', SQLERRM;
    END;
    
    -- 清空目标表数据
    BEGIN
        v_sql := 'DELETE FROM ' || v_to_table || ' WHERE PERIOD_ID = ' || f_period_id;
        EXECUTE v_sql;
        
        -- 记录清空目标表成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '清空目标表数据成功 - 表名：' || v_to_table || ', 期间ID：' || f_period_id,
            F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
            F_RESULT_STATUS => x_result_status,
            F_ERRBUF => C_SUCCESS_MSG
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '清空目标表数据失败: %', SQLERRM;
    END;
    
    -- 获取版本ID
    BEGIN
        v_sql := 'SELECT VERSION_ID FROM ' || v_version_table || 
                ' WHERE DEL_FLAG = ''N'' AND STATUS = 1 AND UPPER(DATA_TYPE) = ''CATEGORY'' ' ||
                ' ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
        EXECUTE v_sql INTO v_version_id;
        
        IF v_version_id IS NULL THEN
            RAISE EXCEPTION '未找到有效的版本ID';
        END IF;
        
        -- 记录版本ID获取成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '版本ID获取成功 - 版本ID：' || v_version_id
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '获取版本ID失败: %', SQLERRM;
    END;
    
    -- 记录配置阶段完成日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '配置阶段完成，开始数据处理阶段'
    );
    
    -- 调用数据处理函数
    PERFORM fin_dm_opt_foi.process_item_data_refactored(
        f_industry_flag, f_period_id, f_caliber_flag, f_dimension_type, f_keystr,
        v_from_table, v_join_table, v_to_table, v_version_id, v_view_begin, v_view_end
    );
    
    -- 收集统计信息
    EXECUTE 'ANALYZE ' || v_to_table;
    
    -- 记录函数执行完成日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => C_SP_NAME || '执行完成 - 统计信息收集完成，目标表：' || v_to_table
    );
    
    RETURN C_SUCCESS_MSG;
    
EXCEPTION
    WHEN OTHERS THEN
        x_result_status := C_FAIL_STATUS;
        
        -- 记录异常日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_CAL_LOG_DESC => C_SP_NAME || '执行失败 - 产业类型：' || f_industry_flag || 
                             ', 维度类型：' || f_dimension_type || ', 业务口径：' || f_caliber_flag ||
                             ', 错误信息：' || SQLERRM,
            F_RESULT_STATUS => x_result_status,
            F_ERRBUF => SQLSTATE || ':' || SQLERRM
        );
        
        RETURN C_FAIL_STATUS;
END;
$$;
